<?php

namespace app\model;

use app\enum\LicensePlan;
use app\lib\Cacheable;
use app\lib\Date;
use app\lib\Lua;
use app\lib\Redis;
use app\notification\GenericSms;
use think\facade\Log;
use think\Model;
use yunwuxin\facade\Notification;

/**
 * Class app\model\LicenseBot
 *
 * @property \app\enum\LicensePlan $plan
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $expire_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property int $partner_id
 * @property int $token
 * @property int $user_id
 * @property string $name
 * @property object $warn
 * @property-read \app\model\AccessToken $access_token
 * @property-read \app\model\Partner $partner
 * @property-read \app\model\User $user
 * @property-read mixed $code
 */
class LicenseBot extends Model
{
    use Cacheable;

    const CACHE_TOKEN_KEY = 'license_bot_token_%s';

    protected $type = [
        'expire_time' => Date::class,
        'plan'        => LicensePlan::class,
    ];

    protected $json = ['warn'];

    public static function onAfterUpdate(self $model): void
    {
        $model->clearCache();
        $model->invoke(function (Redis $redis) use ($model) {
            $key = sprintf(self::CACHE_TOKEN_KEY, $model->id);
            $redis->del($key);
        });
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function partner()
    {
        return $this->belongsTo(Partner::class);
    }

    public function accessToken()
    {
        return $this->morphOne(AccessToken::class, 'accessible');
    }

    public function orders()
    {
        return $this->morphMany(Order::class, 'payable');
    }

    public function isExpired()
    {
        return !empty($this->expire_time) && $this->expire_time->lt(Date::now());
    }

    protected function getCodeAttr()
    {
        return $this->access_token?->token;
    }

    protected function getTokenAttr($token)
    {
        return $this->invoke(function (Redis $redis) use ($token) {
            $key = sprintf(self::CACHE_TOKEN_KEY, $this->id);
            return (int) $redis->eval(Lua::GET_OR_SET, [$key, $token], 1);
        });
    }

    public function consumeTokens($usage)
    {
        $this->checkWarn($usage);
        $this->withEvent(false)
            ->dec('token', $usage)
            ->save();

        $this->invoke(function (Redis $redis) use ($usage) {
            $key = sprintf(self::CACHE_TOKEN_KEY, $this->id);
            $redis->eval(Lua::DECR_BY, [$key, $usage], 1);
        });
    }

    public function checkWarn($usage)
    {
        try {
            if ($this->warn?->token > 0) {
                $warn       = $this->warn->token * 1000;
                $needNotify = $warn < $this->token && $warn >= $this->token - $usage;
                if ($needNotify) {
                    Notification::send(
                        $this->warn->mobile ?: $this->partner->user->mobile,
                        new GenericSms("您站点{$this->getAttr('name')}的Token余额不足，为了避免影响业务，请及时充值！")
                    );
                }
            }
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
        }
    }
}
