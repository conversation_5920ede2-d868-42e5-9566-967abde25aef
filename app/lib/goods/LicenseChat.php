<?php

namespace app\lib\goods;

use app\enum\LicensePlan;
use app\lib\Date;
use app\lib\Goods;
use app\model\CommissionLog;
use app\model\Order;
use think\exception\ValidateException;

class LicenseChat extends Goods
{
    public const NAME = '应用授权';

    public function __construct(protected \app\model\LicenseChat $license, protected $fee, $amount = null)
    {
        if ($amount > 0) {
            if ($amount < $this->fee) {
                throw new ValidateException('售价不能低于代理价');
            }
            $this->amount = floor($amount * 100);
        } else {
            $this->amount = $fee * 100;
        }
    }

    public function canRevoke(Order $order)
    {
        return true;
    }

    public function invoke(Order $order)
    {
        $this->license->inc('token', 10 * 1000 * 1000)->save([
            'plan'        => LicensePlan::Licensed,
            'expire_time' => null,
        ]);

        //分佣
        $commission = $this->amount - $this->fee * 100;
        if ($commission > 0) {
            $this->license->partner->updateCommission(CommissionLog::TYPE_INC, $commission, $this->getSubject(), $order);
        }

        return $this->license;
    }

    public function revoke(Order $order)
    {
        $this->license
            ->dec('token', 10 * 1000 * 1000)
            ->save([
                'plan'        => LicensePlan::Trial,
                'expire_time' => Date::now(),
            ]);

        $order->commission?->delete();
    }

    public function getSubject()
    {
        return '[应用授权] ThinkChat授权';
    }
}
