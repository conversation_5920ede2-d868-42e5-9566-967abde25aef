<?php

namespace app\rpc;

use app\facade\Producer;
use app\lib\Date;
use app\model\AccessToken;
use app\model\ai\Log;
use app\model\ApplicationToken;
use app\model\LicenseBot;
use app\model\LicenseChat;
use app\model\LicenseWiki;
use app\model\User;
use think\Request;
use think\swoole\concerns\WithMiddleware;
use think\swoole\rpc\Protocol;
use Throwable;
use yunwuxin\oauth\server\ResourceServer;

class Ai
{
    use WithMiddleware;

    /**
     * @var User|ApplicationToken|LicenseChat|LicenseBot|LicenseWiki
     */
    protected $user;

    public function __construct()
    {
        $this->middleware(function (Protocol $protocol, $next) {
            $context = $protocol->getContext();

            $this->user = $this->getUser($context['token'] ?? '');

            if (empty($this->user)) {
                abort(401, '未授权');
            }

            return $next($protocol);
        });
    }

    protected function getUser($token)
    {
        $accessToken = AccessToken::getByToken($token);

        if ($accessToken) {
            if ($accessToken->match(User::class, 'ai')) {
                return $accessToken->accessible;
            }
            if ($accessToken->match([LicenseChat::class, LicenseBot::class, LicenseWiki::class])) {
                if ($accessToken->accessible->isExpired()) {
                    return null;
                }
                return $accessToken->accessible;
            }
        }

        if ($accessToken === false) {
            //检查oauth，内部应用使用
            try {
                $server  = app(ResourceServer::class);
                $request = (new Request())->withHeader([
                    'Authorization' => "Bearer {$token}",
                ]);

                $req = $server->validateAuthenticatedRequest($request);

                $accessTokenId = $req->getAttribute('oauth_access_token_id');

                $accessToken = ApplicationToken::where('id', $accessTokenId)
                    ->where('expire_time', '>=', Date::now())
                    ->findOrFail();

                if ($accessToken->can('ai')) {
                    return $accessToken;
                }
            } catch (Throwable) {

            }
        }
    }

    public function check()
    {
        if ($this->user instanceof User) {
            if (empty($this->user->ai)) {
                abort(404, '尚未开通ThinkAI服务');
            }

            if ($this->user->ai->token < 0) {
                abort(403, '余额不足');
            }
        } elseif ($this->user instanceof LicenseWiki || $this->user instanceof LicenseBot || $this->user instanceof LicenseChat) {
            if ($this->user->token < 0) {
                abort(403, 'Token余额不足');
            }
        }
    }

    public function consumeTokens(string $type, string $code, int $usage)
    {
        if ($usage > 0) {
            if ($this->user instanceof User) {
                $userId   = $this->user->id;
                $userType = Log::TYPE_USER;
                $this->user->ai->consumeTokens($usage);
            } elseif ($this->user instanceof LicenseChat) {
                $userId   = $this->user->id;
                $userType = Log::TYPE_LICENSE_CHAT;
                $this->user->consumeTokens($usage);
            } elseif ($this->user instanceof LicenseBot) {
                $userId   = $this->user->id;
                $userType = Log::TYPE_LICENSE_BOT;
                $this->user->consumeTokens($usage);
            } elseif ($this->user instanceof LicenseWiki) {
                $userId   = $this->user->id;
                $userType = Log::TYPE_LICENSE_WIKI;
                $this->user->consumeTokens($usage);
            } elseif ($this->user instanceof ApplicationToken) {
                $userId   = $this->user->application->id;
                $userType = Log::TYPE_APPLICATION;
            } else {
                throw new \Exception('未知用户类型');
            }

            Producer::send('ai_log', json_encode([
                'user_id'     => $userId,
                'user_type'   => $userType,
                'type'        => $type,
                'prompt'      => 0,
                'completion'  => $usage,
                'model'       => $code,
                'create_time' => Date::now(),
            ]));
        }
    }
}
