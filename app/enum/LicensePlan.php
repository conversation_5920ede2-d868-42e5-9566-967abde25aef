<?php

namespace app\enum;

use JsonSerializable;

enum LicensePlan: string implements JsonSerializable
{
    const Fee = [
        'token' => 21,
        'wiki'  => 80000,
        'bot'   => 80000,
        'chat'  => 3000,
        'ai'    => 10000,
    ];

    case Trial = 'trial';
    case Licensed = 'licensed';

    public function label()
    {
        return match ($this) {
            self::Trial => '试用版',
            self::Licensed => '授权版',
        };
    }

    public function jsonSerialize(): mixed
    {
        return [
            'value' => $this->value,
            'label' => $this->label(),
        ];
    }
}
