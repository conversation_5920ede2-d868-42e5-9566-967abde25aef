<?php

namespace app\controller\api;

use app\model\AccessToken;
use app\model\LicenseAi;
use app\model\LicenseBot;
use app\model\LicenseChat;
use app\model\LicenseWiki;

class LicenseController extends ApiController
{
    public function verify()
    {
        $data = $this->validate([
            'type'  => 'require',
            'token' => 'require',
        ]);

        //上报ip
        $ip = $this->request->ip();

        $type = match ($data['type']) {
            'api' => LicenseAi::class,
            'chat' => LicenseChat::class,
            'bot' => LicenseBot::class,
            'wiki' => LicenseWiki::class,
            default => abort(422, '类型不支持'),
        };

        $token = AccessToken::getByToken($data['token'], $type);

        if (empty($token) || $token->accessible->isExpired()) {
            abort(422, '验证未通过');
        }

        return $token->accessible;
    }

    public function read($type, $id)
    {
        $license = match ($type) {
            'ai' => LicenseAi::findOrFail($id),
            'chat' => LicenseChat::findOrFail($id),
            'bot' => LicenseBot::findOrFail($id),
            'wiki' => LicenseWiki::findOrFail($id),
            default => abort(404),
        };

        return $license->append(['code']);
    }
}
