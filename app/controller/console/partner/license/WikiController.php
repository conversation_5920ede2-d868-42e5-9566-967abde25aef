<?php

namespace app\controller\console\partner\license;

use app\controller\console\Controller;
use app\controller\console\partner\WithPartner;
use app\enum\LicensePlan;
use app\lib\Date;
use app\lib\goods\LicenseWikiPackage;
use app\model\LicenseWiki;
use app\Request;
use Ramsey\Uuid\Uuid;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\annotation\route\Put;

class WikiController extends Controller
{
    use WithPartner;

    /** @var LicenseWiki */
    protected $license;

    public function initialized()
    {
        $this->middleware(function (Request $request, $next) {
            $id = $request->route('id');

            if (empty($id)) {
                abort(404);
            }

            $this->license = LicenseWiki::where('partner_id', $this->partner->id)->findOrFail($id);

            return $next($request);
        })->except(['index', 'save']);
    }

    #[Get('partner/license/wiki')]
    public function index()
    {
        return LicenseWiki::where('partner_id', $this->partner->id)->order('plan desc')->order('id desc')->paginate();
    }

    #[Post('partner/license/wiki')]
    public function save()
    {
        $data = $this->validate([
            'name|网站名称' => 'require',
            'url|网站地址'  => 'require',
        ]);

        $data['partner_id']  = $this->partner->id;
        $data['plan']        = LicensePlan::Trial;
        $data['token']       = 100 * 1000;
        $data['expire_time'] = Date::now()->addDays(15);

        LicenseWiki::create($data);
    }

    #[Put('partner/license/wiki/:id')]
    public function update()
    {
        $data = $this->validate([
            'name|网站名称' => 'require',
            'url|网站地址'  => 'require',
        ]);

        $this->license->save($data);
    }

    #[Get('partner/license/wiki/:id/token')]
    public function token()
    {
        $accessToken = $this->license->access_token;

        if (empty($accessToken)) {
            $accessToken = $this->license->accessToken()->save([
                'name'  => '授权令牌',
                'token' => Uuid::uuid4()->toString(),
            ]);
        }

        return ['token' => $accessToken->token];
    }

    #[Post('partner/license/wiki/:id/renew')]
    public function renew()
    {
        if ($this->license->plan != LicensePlan::Trial) {
            abort(400);
        }

        $this->license->transaction(function () {
            $this->license->save([
                'expire_time' => Date::now()->addDays(15),
                'token'       => max($this->license->token, 100 * 1000),
            ]);

            $this->license->access_token?->save([
                'token' => Uuid::uuid4()->toString(),
            ]);
        });
    }

    #[Post('partner/license/wiki/:id/recharge')]
    public function recharge()
    {
        $data = $this->validate([
            'nums'   => 'require|integer|gt:0',
            'amount' => 'float',
        ]);

        $goods = new LicenseWikiPackage($this->license, $data['nums'], $data['amount'] ?? null);
        $goods->setUser($this->user);

        $order = $goods->purchase();

        return $order->pay();
    }

    #[Post('partner/license/wiki/:id/buy')]
    public function buy()
    {
        if (empty($this->partner->fee['wiki'])) {
            abort(400);
        }

        $data = $this->validate([
            'amount' => 'float',
        ]);

        $goods = new \app\lib\goods\LicenseWiki($this->license, $this->partner->fee['wiki'], $data['amount'] ?? null);
        $goods->setUser($this->user);

        $order = $goods->purchase();

        return $order->pay();
    }

    #[Get('partner/license/wiki/:id/order')]
    public function order()
    {
        return $this->license->orders()->where('status', '<>', 0)->order('create_time desc')->paginate();
    }

    #[Post('partner/license/wiki/:id/warn')]
    public function warn()
    {
        $data = $this->validate([
            'token|预警余额'  => 'require|integer',
            'mobile|手机号码' => 'mobile',
        ]);

        $this->license->save([
            'warn' => $data,
        ]);
    }
}
