<?php

namespace app\controller\console\partner\license;

use app\controller\console\Controller;
use app\controller\console\partner\WithPartner;
use app\enum\LicensePlan;
use app\lib\Date;
use app\lib\goods\LicenseChatPackage;
use app\model\LicenseChat;
use app\Request;
use Ramsey\Uuid\Uuid;
use think\annotation\route\Get;
use think\annotation\route\Post;
use think\annotation\route\Put;

class ChatController extends Controller
{
    use WithPartner;

    /** @var LicenseChat */
    protected $license;

    public function initialized()
    {
        $this->middleware(function (Request $request, $next) {
            $id = $request->route('id');

            if (empty($id)) {
                abort(404);
            }

            $this->license = LicenseChat::where('partner_id', $this->partner->id)->findOrFail($id);

            return $next($request);
        })->except(['index', 'save']);
    }

    #[Get('partner/license/chat')]
    public function index()
    {
        return LicenseChat::where('partner_id', $this->partner->id)->order('plan desc')->order('id desc')->paginate();
    }

    #[Post('partner/license/chat')]
    public function save()
    {
        $data = $this->validate([
            'name|网站名称' => 'require',
        ]);

        $data['partner_id']  = $this->partner->id;
        $data['plan']        = LicensePlan::Trial;
        $data['token']       = 100 * 1000;
        $data['expire_time'] = Date::now()->addDays(15);

        LicenseChat::create($data);
    }

    #[Put('partner/license/chat/:id')]
    public function update()
    {
        $data = $this->validate([
            'name|网站名称' => 'require',
        ]);

        $this->license->save($data);
    }

    #[Get('partner/license/chat/:id/token')]
    public function token()
    {
        $accessToken = $this->license->access_token;

        if (empty($accessToken)) {
            $accessToken = $this->license->accessToken()->save([
                'name'  => '授权令牌',
                'token' => Uuid::uuid4()->toString(),
            ]);
        }

        return ['token' => $accessToken->token];
    }

    #[Post('partner/license/chat/:id/renew')]
    public function renew()
    {
        if ($this->license->plan != LicensePlan::Trial) {
            abort(400);
        }

        $this->license->transaction(function () {
            $this->license->save([
                'expire_time' => Date::now()->addDays(15),
                'token'       => max($this->license->token, 100 * 1000),
            ]);

            $this->license->access_token?->save([
                'token' => Uuid::uuid4()->toString(),
            ]);
        });
    }

    #[Post('partner/license/chat/:id/recharge')]
    public function recharge()
    {
        $data = $this->validate([
            'nums'   => 'require|integer|gt:0',
            'amount' => 'float',
        ]);

        $goods = new LicenseChatPackage($this->license, $data['nums'], $data['amount'] ?? null);
        $goods->setUser($this->user);
        $order = $goods->purchase();

        return $order->pay();
    }

    #[Post('partner/license/chat/:id/buy')]
    public function buy()
    {
        if (empty($this->partner->fee['chat'])) {
            abort(400);
        }

        $data = $this->validate([
            'amount' => 'float',
        ]);

        $goods = new \app\lib\goods\LicenseChat($this->license, $this->partner->fee['chat'], $data['amount'] ?? null);
        $goods->setUser($this->user);
        $order = $goods->purchase();

        return $order->pay();
    }

    #[Get('partner/license/chat/:id/order')]
    public function order()
    {
        return $this->license->orders()->where('status', '<>', 0)->order('create_time desc')->paginate();
    }

    #[Post('partner/license/chat/:id/warn')]
    public function warn()
    {
        $data = $this->validate([
            'token|预警余额'  => 'require|integer',
            'mobile|手机号码' => 'mobile',
        ]);

        $this->license->save([
            'warn' => $data,
        ]);
    }
}
