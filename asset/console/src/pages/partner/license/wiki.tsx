import Renew from '@/pages/partner/license/components/renew';
import { formatLongNumber } from '@/utils/format';
import { dayjs, ModalForm, Space, Table, useRouteLoaderData } from '@topthink/common';
import Buy from './components/buy';
import OrderModal from './components/order-model';
import Recharge from './components/recharge';
import Token from './components/token';

export const Component = () => {
    const partner = useRouteLoaderData('partner') as Partner;

    return <Table
        toolBarRender={(action) => {
            return <ModalForm
                text={'创建授权码'}
                action={`/partner/license/wiki`}
                schema={{
                    type: 'object',
                    properties: {
                        name: {
                            title: '网站名称',
                            type: 'string'
                        },
                        url: {
                            title: '网站地址',
                            type: 'string',
                            description: '请求正确填写网站的访问地址，否则会导致无法编辑文档'
                        }
                    }
                }}
                uiSchema={{
                    url: {
                        'ui:options': {
                            placeholder: 'http://www.topthink.com'
                        }
                    }
                }}
                onSuccess={() => {
                    action.reload();
                }}
            />;
        }}
        source={'/partner/license/wiki'}
        columns={[
            {
                title: '站点名称',
                dataIndex: 'name',
                render({ record }) {
                    return <a className={'link-primary'} target={'_blank'} href={record.url}>{record.name}</a>;
                }
            },
            {
                title: '版本',
                dataIndex: 'plan',
                width: 100,
                render({ value }) {
                    return <div className={value.value == 'trial' ? 'text-orange' : 'text-success'}>{value.label}</div>;
                }
            },
            {
                title: '授权码',
                key: 'code',
                width: 380,
                render({ record }) {
                    return <Token key={record.expire_time} url={`/partner/license/wiki/${record.id}/token`} />;
                }
            },
            {
                title: 'Token 余额',
                dataIndex: 'token',
                width: 150,
                render({ value, record, action }) {
                    return <Space size={5}>
                        {formatLongNumber(value)}
                        <Recharge url={`/partner/license/wiki/${record.id}/recharge`} onSuccess={() => action.reload()} />
                        <ModalForm
                            onSuccess={action.reload}
                            method={'post'}
                            action={`/partner/license/wiki/${record.id}/warn`}
                            modalProps={{ header: '预警设置' }}
                            formContext={{ layout: 'horizontal' }}
                            formData={{
                                token: record.warn?.token || 0,
                                mobile: record.warn?.mobile || ''
                            }}
                            schema={{
                                type: 'object',
                                properties: {
                                    token: {
                                        title: '预警余额',
                                        type: 'number',
                                        minimum: 0,
                                    },
                                    mobile: {
                                        title: '手机号码',
                                        type: 'string'
                                    }
                                }
                            }}
                            uiSchema={{
                                token: {
                                    'ui:options': {
                                        'suffix': 'K'
                                    },
                                    'ui:help': '低于预警余额将发送短信提醒，设置为0的时候关闭预警',
                                },
                                mobile: {
                                    'ui:help': '留空则使用当前账号的手机号',
                                }
                            }}
                            text={'预警'}
                        />
                    </Space>;
                }
            },
            {
                title: '有效日期',
                dataIndex: 'expire_time',
                width: 150,
                render({ value, record, action }) {
                    if (value) {
                        if (dayjs(value).isBefore(dayjs())) {
                            return <Space>
                                <div className='text-danger'>已过期</div>
                                {record.plan.value == 'trial' && <Renew
                                    url={`/partner/license/wiki/${record.id}/renew`}
                                    onSuccess={() => action.reload()}
                                />}
                            </Space>;
                        }
                        return value;
                    }
                    return '--';
                }
            },
            {
                title: '创建日期',
                dataIndex: 'create_time',
                width: 150,
            },
            {
                title: '操作',
                key: 'action',
                width: 150,
                align: 'right',
                render({ record, action }) {
                    return <Space>
                        {record.plan.value == 'trial' && <Buy
                            url={`/partner/license/wiki/${record.id}/buy`}
                            fee={partner.fee?.wiki}
                            onSuccess={() => action.reload()}
                        />}
                        <ModalForm
                            text={'编辑'}
                            action={`/partner/license/wiki/${record.id}`}
                            method={'put'}
                            formData={{
                                name: record.name,
                                url: record.url
                            }}
                            schema={{
                                type: 'object',
                                properties: {
                                    name: {
                                        title: '网站名称',
                                        type: 'string'
                                    },
                                    url: {
                                        title: '网站地址',
                                        type: 'string',
                                        description: '请求正确填写网站的访问地址，否则会导致无法编辑文档'
                                    }
                                }
                            }}
                            uiSchema={{
                                url: {
                                    'ui:options': {
                                        placeholder: 'http://www.topthink.com'
                                    }
                                }
                            }}
                            onSuccess={() => {
                                action.reload();
                            }}
                        />
                        <OrderModal source={`/partner/license/wiki/${record.id}/order`} />
                    </Space>;
                }
            }
        ]}
    />;
};
